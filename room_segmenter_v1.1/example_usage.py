#!/usr/bin/env python3
"""
使用示例：在餐桌周围找到最近的竖直和水平边界构成矩形区域
"""

import cv2
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processors import RegionProcessor
from config import Config

def example_usage():
    """使用示例"""
    print("=== 餐桌周围边界检测使用示例 ===")
    
    # 假设你有一个图像和检测结果
    # 这里我们创建一个简单的示例
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # 模拟检测结果
    # 所有检测框：包括餐桌、门、其他家具等
    all_boxes = [
        (250, 150, 350, 220),  # 餐桌
        (180, 140, 200, 180),  # 门1
        (400, 140, 420, 180),  # 门2
        (100, 100, 150, 150),  # 其他家具
    ]
    all_classes = [
        Config.get_class_id_by_name("dining_table_set"),  # 餐桌
        Config.get_class_id_by_name("door"),              # 门1
        Config.get_class_id_by_name("door"),              # 门2
        Config.get_class_id_by_name("sofa_grounded"),     # 其他家具
    ]
    
    # 提取餐桌的检测框
    table_boxes = []
    table_classes = []
    for box, cls in zip(all_boxes, all_classes):
        if cls == Config.get_class_id_by_name("dining_table_set"):
            table_boxes.append(box)
            table_classes.append(cls)
    
    # 创建处理器
    processor = RegionProcessor()
    
    # 方法1：不考虑门的阻挡
    print("\n方法1：不考虑门的阻挡")
    dining_areas_simple = processor.find_nearest_boundaries_around_table(
        image, table_boxes, table_classes
    )
    
    for i, area in enumerate(dining_areas_simple):
        print(f"餐厅区域 {i+1}: 左={area['left']}, 右={area['right']}, 上={area['top']}, 下={area['bottom']}")
    
    # 方法2：考虑门的阻挡（推荐）
    print("\n方法2：考虑门的阻挡（推荐）")
    dining_areas_with_doors = processor.find_nearest_boundaries_around_table(
        image, table_boxes, table_classes, all_boxes, all_classes
    )
    
    for i, area in enumerate(dining_areas_with_doors):
        print(f"餐厅区域 {i+1}: 左={area['left']}, 右={area['right']}, 上={area['top']}, 下={area['bottom']}")
        print(f"  原始餐桌位置: {area['table_box']}")
        print(f"  区域大小: {area['right']-area['left']} x {area['bottom']-area['top']}")
    
    return dining_areas_with_doors

def integrate_with_existing_code():
    """展示如何与现有代码集成"""
    print("\n=== 与现有代码集成示例 ===")
    
    # 假设你已经有了现有的房间分割代码
    processor = RegionProcessor()
    
    # 假设的输入数据
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    boxes = [(250, 150, 350, 220), (180, 140, 200, 180)]  # 餐桌和门
    classes = [Config.get_class_id_by_name("dining_table_set"), Config.get_class_id_by_name("door")]
    
    # 提取餐桌
    table_boxes = [box for box, cls in zip(boxes, classes) 
                   if cls == Config.get_class_id_by_name("dining_table_set")]
    table_classes = [cls for cls in classes 
                     if cls == Config.get_class_id_by_name("dining_table_set")]
    
    if table_boxes:
        # 使用新的边界检测方法
        dining_areas = processor.find_nearest_boundaries_around_table(
            image, table_boxes, table_classes, boxes, classes
        )
        
        print(f"检测到 {len(dining_areas)} 个餐厅区域")
        
        # 你可以将这些区域用于后续处理
        for i, area in enumerate(dining_areas):
            # 创建餐厅区域掩码
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            cv2.rectangle(mask, (area['left'], area['top']), 
                         (area['right'], area['bottom']), 255, -1)
            
            print(f"餐厅区域 {i+1} 掩码已创建，面积: {np.sum(mask > 0)} 像素")
    
    else:
        print("未检测到餐桌")

def main():
    """主函数"""
    example_usage()
    integrate_with_existing_code()
    
    print("\n=== 使用说明 ===")
    print("1. find_nearest_boundaries_around_table() 方法可以找到餐桌周围最近的边界")
    print("2. 如果提供 all_boxes 和 all_classes 参数，会考虑门的阻挡")
    print("3. 返回的区域包含 'left', 'right', 'top', 'bottom' 和 'table_box' 字段")
    print("4. 这个方法可以替代或补充现有的 grow_dining_area_from_table() 方法")

if __name__ == "__main__":
    main()
