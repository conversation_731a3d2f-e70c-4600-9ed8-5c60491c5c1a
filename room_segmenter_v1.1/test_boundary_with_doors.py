#!/usr/bin/env python3
"""
测试餐桌周围边界检测功能（包含门的阻挡检测）
"""

import cv2
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processors import RegionProcessor
from config import Config

def create_test_image_with_doors():
    """创建包含门的测试图像"""
    # 创建一个白色背景的图像
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255

    # 添加一些灰色区域作为墙壁
    cv2.rectangle(image, (0, 0), (600, 50), (128, 128, 128), -1)  # 上墙
    cv2.rectangle(image, (0, 350), (600, 400), (128, 128, 128), -1)  # 下墙
    cv2.rectangle(image, (0, 0), (50, 400), (128, 128, 128), -1)  # 左墙
    cv2.rectangle(image, (550, 0), (600, 400), (128, 128, 128), -1)  # 右墙

    # 添加一些内部墙壁，这些墙壁会阻挡餐桌的边界检测
    cv2.rectangle(image, (180, 50), (200, 350), (128, 128, 128), -1)  # 左侧竖墙
    cv2.rectangle(image, (400, 50), (420, 350), (128, 128, 128), -1)  # 右侧竖墙
    cv2.rectangle(image, (50, 100), (550, 120), (128, 128, 128), -1)  # 上方横墙
    cv2.rectangle(image, (50, 280), (550, 300), (128, 128, 128), -1)  # 下方横墙

    return image

def test_boundary_detection_with_doors():
    """测试包含门的边界检测"""
    print("=== 测试餐桌周围边界检测（包含门的阻挡） ===")
    
    # 创建测试图像
    image = create_test_image_with_doors()
    
    # 定义餐桌位置（在中央区域，被墙壁包围）
    table_boxes = [(250, 150, 350, 220)]  # 餐桌在中央区域
    table_classes = [Config.get_class_id_by_name("dining_table_set")]

    # 定义门的位置，这些门会阻挡餐桌的边界检测路径
    door_boxes = [
        (180, 140, 200, 180),   # 左侧竖墙上的门（会阻挡向左的边界检测）
        (400, 140, 420, 180),   # 右侧竖墙上的门（会阻挡向右的边界检测）
        (220, 100, 260, 120),   # 上方横墙上的门（会阻挡向上的边界检测）
        (320, 280, 360, 300)    # 下方横墙上的门（会阻挡向下的边界检测）
    ]
    door_classes = [Config.get_class_id_by_name("door")] * len(door_boxes)
    
    # 合并所有检测框
    all_boxes = table_boxes + door_boxes
    all_classes = table_classes + door_classes
    
    # 创建处理器
    processor = RegionProcessor()
    
    # 测试新的边界检测方法
    dining_areas = processor.find_nearest_boundaries_around_table(
        image, table_boxes, table_classes, all_boxes, all_classes
    )
    
    print(f"检测到 {len(dining_areas)} 个餐厅区域")
    
    # 可视化结果
    result_image = image.copy()
    
    # 绘制餐桌
    for (x1, y1, x2, y2) in table_boxes:
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 128, 255), 2)
        cv2.putText(result_image, "Table", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 128, 255), 1)
    
    # 绘制门
    for (x1, y1, x2, y2) in door_boxes:
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 255), 2)
        cv2.putText(result_image, "Door", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 1)
    
    # 绘制检测到的餐厅区域
    for i, area in enumerate(dining_areas):
        left, right = area['left'], area['right']
        top, bottom = area['top'], area['bottom']
        
        print(f"餐厅区域 {i+1}:")
        print(f"  左边界: {left}")
        print(f"  右边界: {right}")
        print(f"  上边界: {top}")
        print(f"  下边界: {bottom}")
        print(f"  区域大小: {right-left} x {bottom-top}")
        
        # 绘制餐厅区域边界
        cv2.rectangle(result_image, (left, top), (right, bottom), (255, 128, 128), 2)
        cv2.putText(result_image, f"Dining Area {i+1}", (left+5, top+20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 128, 128), 2)
    
    # 保存结果
    output_path = "test_boundary_with_doors_result.jpg"
    cv2.imwrite(output_path, result_image)
    print(f"\n结果已保存到: {output_path}")
    
    return dining_areas

def compare_with_without_doors():
    """比较有门和无门时的边界检测结果"""
    print("\n=== 比较有门和无门的检测结果 ===")
    
    # 创建测试图像
    image = create_test_image_with_doors()
    
    # 定义餐桌位置
    table_boxes = [(250, 150, 350, 220)]
    table_classes = [Config.get_class_id_by_name("dining_table_set")]

    # 定义门的位置
    door_boxes = [
        (180, 140, 200, 180),   # 左侧竖墙上的门
        (400, 140, 420, 180),   # 右侧竖墙上的门
        (220, 100, 260, 120),   # 上方横墙上的门
        (320, 280, 360, 300)    # 下方横墙上的门
    ]
    door_classes = [Config.get_class_id_by_name("door")] * len(door_boxes)
    
    processor = RegionProcessor()
    
    # 不考虑门的检测
    areas_without_doors = processor.find_nearest_boundaries_around_table(
        image, table_boxes, table_classes
    )
    
    # 考虑门的检测
    all_boxes = table_boxes + door_boxes
    all_classes = table_classes + door_classes
    areas_with_doors = processor.find_nearest_boundaries_around_table(
        image, table_boxes, table_classes, all_boxes, all_classes
    )
    
    print("不考虑门的结果:")
    for i, area in enumerate(areas_without_doors):
        print(f"  区域 {i+1}: 左={area['left']}, 右={area['right']}, 上={area['top']}, 下={area['bottom']}")
    
    print("考虑门的结果:")
    for i, area in enumerate(areas_with_doors):
        print(f"  区域 {i+1}: 左={area['left']}, 右={area['right']}, 上={area['top']}, 下={area['bottom']}")

if __name__ == "__main__":
    test_boundary_detection_with_doors()
    compare_with_without_doors()
    print("\n测试完成！")
