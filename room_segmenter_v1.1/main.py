import os
from pathlib import Path
from models import Detector
from processors import RegionProcessor
from visualizer import ResultVisualizer
from utils import ensure_dir, get_image_files, get_label_path
from config import Config
import cv2
import numpy as np

class RoomSegmenter:
    """房间分割与分类主类"""
    
    def __init__(self, image_dir, label_dir, model_path, output_dir=Config.OUTPUT_DIR):
        self.image_dir = image_dir
        self.label_dir = label_dir
        self.output_dir = ensure_dir(output_dir)
        self.detector = Detector(model_path)
        self.processor = RegionProcessor()
        self.visualizer = ResultVisualizer()
        self.config = Config()
    
    def process_single_image(self, image_path, label_path):
        """处理单张图像"""
        img_name = Path(image_path).stem
        image = cv2.imread(image_path)
        if image is None:
            print(f"⚠️ Failed to load image: {image_path}")
            return
            
        h, w = image.shape[:2]
        
        # 加载标签和检测结果
        boxes_gt, classes_gt = self.detector.load_yolo_labels(label_path, w, h)
        result = self.detector.predict(image_path)
        
        if not result:
            print(f"⚠️ No detection results for {image_path}")
            return
            
        boxes, classes, confidence_scores = self.detector.get_boxes_and_classes(result)
        
        # 处理图像并获取区域
        merged_labels, region_areas, binary = self.processor.process_binary_image(image, boxes, classes)
        
        # 分类房间
        room_type_for_label = self.processor.classify_rooms(merged_labels, boxes, classes, region_areas)
        cv2.imwrite(f"binary_{img_name}.png", binary)
        # 如果启用四边向外生长算法，先处理餐厅区域
        if ENABLE_GROWTH_ALGORITHM:
            # 筛选餐桌
            table_boxes = [box for box, cls in zip(boxes, classes) if cls == self.config.DINING_TABLE_CLASS_ID]
            table_classes = [cls for cls in classes if cls == self.config.DINING_TABLE_CLASS_ID]
            
            if table_boxes:
                # 使用四边向外生长算法获取餐厅区域
                dining_areas = self.processor.grow_dining_area_from_table(image, binary, table_boxes, table_classes)
                # dining_areas = self.processor.find_nearest_boundaries_around_table(image, binary, table_boxes, table_classes, boxes, classes)
                # 将生长的餐厅区域合并到连通域中
                for dining_area in dining_areas:
                    # 在连通域中标记餐厅区域
                    x1, y1, x2, y2 = dining_area['left'], dining_area['top'], dining_area['right'], dining_area['bottom']
                    # 在餐厅区域内设置特殊标记
                    merged_labels[y1:y2, x1:x2] = np.max(merged_labels) + 1
                    room_type_for_label[np.max(merged_labels)] = self.config.ROOM_TYPE_MAP[self.config.DINING_TABLE_CLASS_ID]
        
        # 合并同类区域
        merged_labels, room_type_for_label = self.processor.bridge_merge_labels(
            merged_labels, room_type_for_label
        )
        _, region_areas,_ = self.processor.merge_regions(merged_labels, np.max(merged_labels) + 1,binary)
        
        # 可视化
        vis_image, _ = self.visualizer.draw_rooms(image, merged_labels, room_type_for_label, boxes, classes)
        boxed_image = self.visualizer.draw_boxes(image, boxes, classes, confidence_scores)
        gt_image = self.visualizer.draw_boxes(image, boxes_gt, classes_gt)
        
        # 创建并保存最终结果
        final_image = self.visualizer.create_final_result(vis_image, boxed_image, gt_image)
        output_path = os.path.join(self.output_dir, f"{img_name}_final_result.png")
        cv2.imwrite(output_path, final_image)
        
        print(f"✅ Processed {img_name}, Saved to {output_path}")

    def batch_process(self):
        """批量处理所有图像"""
        for img_file in get_image_files(self.image_dir):
            img_path = os.path.join(self.image_dir, img_file)
            label_path = get_label_path(img_file, self.label_dir)
            
            if not os.path.exists(label_path):
                print(f"⚠️ Label not found for {img_file}")
                continue
                
            self.process_single_image(img_path, label_path)

if __name__ == "__main__":
    # 配置路径参数
    # IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/images/val"
    # LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/labels/val"

    IMAGE_DIR = "/home/<USER>/panpan/code/segmentRoom/data_diningroom/images"
    LABEL_DIR = "/home/<USER>/panpan/code/segmentRoom/data_diningroom/labels"

    MODEL_PATH = "/home/<USER>/panpan/code/ultralytics-main/runs/detect/train2/weights/best.pt"
    OUTPUT_DIR = "output"
    ENABLE_GROWTH_ALGORITHM = True
    # 创建并运行房间分割器
    segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, MODEL_PATH, OUTPUT_DIR)
    segmenter.batch_process()