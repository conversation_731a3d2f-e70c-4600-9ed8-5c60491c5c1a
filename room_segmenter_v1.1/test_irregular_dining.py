#!/usr/bin/env python3
"""
测试不规则餐厅区域检测功能
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from processors import RegionProcessor
from config import Config

def create_test_image_with_irregular_room():
    """创建一个包含不规则房间形状的测试图像"""
    # 创建白色背景图像
    h, w = 400, 600
    image = np.ones((h, w, 3), dtype=np.uint8) * 255
    
    # 创建一个L形的房间区域（白色可通行区域）
    # 主要矩形区域
    cv2.rectangle(image, (50, 50), (550, 300), (255, 255, 255), -1)
    
    # 扩展区域（形成L形）
    cv2.rectangle(image, (300, 300), (550, 350), (255, 255, 255), -1)
    
    # 添加一些黑色障碍物/墙壁来创建更复杂的形状
    cv2.rectangle(image, (200, 100), (250, 150), (0, 0, 0), -1)  # 小障碍物
    cv2.rectangle(image, (400, 200), (450, 250), (0, 0, 0), -1)  # 另一个障碍物
    
    # 添加一些门（黑色矩形）
    cv2.rectangle(image, (100, 50), (120, 70), (0, 0, 0), -1)   # 上方门
    cv2.rectangle(image, (530, 150), (550, 170), (0, 0, 0), -1) # 右侧门
    
    return image

def create_test_boxes():
    """创建测试用的检测框"""
    # 餐桌位置（在L形区域的主要部分）
    table_boxes = [(150, 120, 200, 160)]
    table_classes = [Config.get_class_id_by_name("dining_table")]
    
    # 椅子位置
    chair_boxes = [
        (130, 100, 150, 120),  # 餐桌左侧椅子
        (200, 100, 220, 120),  # 餐桌右侧椅子
        (150, 90, 170, 110),   # 餐桌上方椅子
        (150, 160, 170, 180),  # 餐桌下方椅子
    ]
    chair_classes = [Config.get_class_id_by_name("chair")] * len(chair_boxes)
    
    # 门的位置
    door_boxes = [
        (100, 50, 120, 70),    # 上方门
        (530, 150, 550, 170),  # 右侧门
    ]
    door_classes = [Config.get_class_id_by_name("door")] * len(door_boxes)
    
    # 合并所有检测框
    all_boxes = table_boxes + chair_boxes + door_boxes
    all_classes = table_classes + chair_classes + door_classes
    
    return table_boxes, table_classes, all_boxes, all_classes

def test_irregular_dining_detection():
    """测试不规则餐厅区域检测"""
    print("开始测试不规则餐厅区域检测...")
    
    # 创建测试数据
    image = create_test_image_with_irregular_room()
    table_boxes, table_classes, all_boxes, all_classes = create_test_boxes()
    
    # 创建处理器
    processor = RegionProcessor()
    
    # 处理二值图像
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)
    
    # 在二值图像上绘制家具区域
    for (x1, y1, x2, y2), cls in zip(all_boxes, all_classes):
        if cls == Config.get_class_id_by_name("door"):  # 门 - 设置为背景
            cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
        else:  # 家具 - 设置为前景
            cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)
    
    print(f"检测到 {len(table_boxes)} 个餐桌")
    
    # 测试新的不规则餐厅区域检测方法
    dining_areas = processor.grow_dining_area_from_table_irregular(
        image, binary, table_boxes, table_classes
    )
    
    print(f"检测到 {len(dining_areas)} 个不规则餐厅区域")
    
    # 可视化结果
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 原始图像
    axes[0, 0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title("原始图像")
    axes[0, 0].axis('off')
    
    # 二值图像
    axes[0, 1].imshow(binary, cmap='gray')
    axes[0, 1].set_title("二值图像（处理后）")
    axes[0, 1].axis('off')
    
    # 绘制检测框
    result_image = image.copy()
    for (x1, y1, x2, y2) in table_boxes:
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 128, 255), 2)
        cv2.putText(result_image, "Table", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 128, 255), 1)
    
    axes[1, 0].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    axes[1, 0].set_title("检测框")
    axes[1, 0].axis('off')
    
    # 不规则餐厅区域
    if dining_areas:
        dining_result = image.copy()
        for i, area in enumerate(dining_areas):
            mask = area['mask']
            table_box = area['table_box']
            growth_distances = area['growth_distances']
            
            # 在原图上叠加餐厅区域（半透明）
            colored_mask = np.zeros_like(dining_result)
            colored_mask[mask > 0] = [0, 255, 0]  # 绿色
            dining_result = cv2.addWeighted(dining_result, 0.7, colored_mask, 0.3, 0)
            
            # 绘制餐桌框
            x1, y1, x2, y2 = table_box
            cv2.rectangle(dining_result, (x1, y1), (x2, y2), (255, 0, 0), 2)
            
            # 显示生长距离信息
            print(f"餐厅区域 {i+1} 生长距离: {growth_distances}")
            print(f"餐厅区域 {i+1} 面积: {np.sum(mask)} 像素")
        
        axes[1, 1].imshow(cv2.cvtColor(dining_result, cv2.COLOR_BGR2RGB))
        axes[1, 1].set_title("不规则餐厅区域")
    else:
        axes[1, 1].text(0.5, 0.5, "未检测到餐厅区域", ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title("不规则餐厅区域")
    
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('room_segmenter_v1.1/test_irregular_dining_result.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return dining_areas

if __name__ == "__main__":
    dining_areas = test_irregular_dining_detection()
    print("测试完成！")
